# Strategy001 交易策略说明

## 策略概述

Strategy001是一个基于前4笔tick数据和N分钟模型的量化交易策略，主要包含以下几个模块：

### AA模块：前4笔逻辑
- 监控开盘前4笔tick数据（092500, 093000, 093003, 093006, 093009）
- 根据价格关系和成交量条件触发买入
- 资金采用N等分管理，触发一次用完一次

### BB模块：N分钟模型  
- 第5笔后至N分钟内的涨幅监控
- 单笔最大换手率条件检查
- 延时观察和板块优先级比较

### 撤单逻辑
- 指定时刻未再次封板自动撤单
- 换手率异常撤单
- 委托买一量条件撤单

## 核心功能

### 1. AA模块触发条件
```python
# 条件1：093000价小于092500价，第2笔价大于092500价
if t1.last_price < t0.last_price and t2.last_price > t0.last_price:
    trigger_condition = True

# 条件2：093000和093003都小于092500价，第3笔和第4笔都大于092500价  
elif (t1.last_price < t0.last_price and t2.last_price < t0.last_price and 
      t3.last_price > t0.last_price and t4.last_price > t0.last_price):
    trigger_condition = True
```

### 2. 涨停价特殊处理
- 股价4笔内等于涨停价时，计算涨停价连续N笔的委托买一量换手率
- 换手率>=x时以第一等分额买入一笔

### 3. 资金管理
- AA模块：资金N等分，每次触发使用一份
- BB模块：每次使用现金的1/m
- 单笔最大买入限制：100万
- 最大持仓个股数：N支

## 配置参数

### 主要参数说明
```python
STRATEGY_PARAMS = {
    # AA模块参数
    'aa_max_turnover_rate': 2.0,  # 最大换手率阈值(%)
    'aa_max_amount': 5000,        # 最大成交额阈值(千万)
    'aa_fund_parts': 4,           # 资金等分数
    
    # BB模块参数  
    'bb_time_window': 30,         # 时间窗口(分钟)
    'bb_fund_ratio': 0.25,        # 资金使用比例
    'bb_rise_threshold': 3.0,     # 涨幅阈值(%)
    
    # 资金管理
    'max_positions': 10,          # 最大持仓数
    'max_single_amount': 1000000, # 单笔最大金额
    'buy_deadline': '143000',     # 买入截止时间
}
```

## 使用方法

### 1. 基本使用
```python
from strategy.st_001 import Strategy001
from strategy.st_001_config import STRATEGY_PARAMS, SECTOR_STOCKS

# 创建策略实例
strategy = Strategy001()

# 更新配置
strategy.strategy_params.update(STRATEGY_PARAMS)
strategy.update_sector_stocks(SECTOR_STOCKS)

# 启动策略
strategy.start()
```

### 2. 运行示例
```bash
# 运行策略
python strategy/st_001_example.py

# 测试模式
python strategy/st_001_example.py test
```

### 3. 状态监控
```python
# 获取策略状态
status = strategy.get_strategy_status()
print(status)
```

## 关键方法说明

### 核心处理方法
- `process_tick()`: 处理单个tick数据
- `check_aa_strategy()`: AA模块策略检查
- `check_bb_strategy()`: BB模块策略检查
- `check_cancel_conditions()`: 撤单条件检查

### 交易执行方法
- `execute_aa_buy()`: 执行AA模块买入
- `execute_bb_buy()`: 执行BB模块买入
- `send_buy_order()`: 发送买入订单
- `cancel_order()`: 撤销订单

### 辅助计算方法
- `calculate_turnover_rate()`: 计算换手率
- `calculate_consecutive_bid_turnover()`: 计算连续委托买一量换手率
- `check_sector_priority()`: 检查板块优先级

## 注意事项

1. **数据依赖**: 策略依赖实时tick数据，需要确保数据源稳定
2. **资金管理**: 需要根据实际账户资金调整配置参数
3. **风险控制**: 建议在模拟环境充分测试后再用于实盘
4. **板块配置**: 需要根据实际需求配置板块股票映射
5. **交易接口**: 需要实现具体的下单和撤单接口

## 扩展功能

策略支持以下扩展：
- 自定义板块优先级算法
- 动态参数调整
- 多策略组合运行
- 风险监控和报警
- 交易记录和分析

## 日志和监控

策略包含完整的日志记录功能：
- 买入卖出记录
- 撤单原因记录
- 异常情况记录
- 策略状态监控
