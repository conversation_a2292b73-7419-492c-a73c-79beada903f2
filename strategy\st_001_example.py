#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy001 使用示例
"""

import sys
import os
import time
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy.st_001 import Strategy001
from strategy.st_001_config import STRATEGY_PARAMS, SECTOR_STOCKS, WATCH_STOCKS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy001.log'),
        logging.StreamHandler()
    ]
)

def main():
    """主函数"""
    logger = logging.getLogger(__name__)
    logger.info("启动Strategy001示例")
    
    try:
        # 创建策略实例
        strategy = Strategy001()
        
        # 更新策略参数（可选）
        strategy.strategy_params.update(STRATEGY_PARAMS)
        
        # 更新板块股票映射
        strategy.update_sector_stocks(SECTOR_STOCKS)
        
        # 启动策略
        strategy.start()
        
        logger.info("策略已启动，按Ctrl+C停止")
        
        # 主循环 - 监控策略状态
        while True:
            time.sleep(10)  # 每10秒检查一次状态
            
            status = strategy.get_strategy_status()
            logger.info(f"策略状态: {status}")
            
            # 可以在这里添加其他监控逻辑
            
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    except Exception as e:
        logger.error(f"策略运行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'strategy' in locals():
            strategy.stop()
        logger.info("策略已停止")

def test_strategy_methods():
    """测试策略方法"""
    logger = logging.getLogger(__name__)
    logger.info("测试策略方法")
    
    strategy = Strategy001()
    
    # 测试资金管理器
    fund_manager = strategy.fund_manager
    logger.info(f"总资金: {fund_manager.total_funds}")
    logger.info(f"AA模块单次金额: {fund_manager.get_aa_fund_amount()}")
    logger.info(f"BB模块单次金额: {fund_manager.get_bb_fund_amount()}")
    
    # 测试配置更新
    strategy.update_sector_stocks(SECTOR_STOCKS)
    logger.info(f"板块数量: {len(strategy.sector_stocks)}")
    
    # 测试状态获取
    status = strategy.get_strategy_status()
    logger.info(f"初始状态: {status}")

if __name__ == "__main__":
    # 可以选择运行主程序或测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_strategy_methods()
    else:
        main()
