from core.context import context_manager
from core.object import TickData
from core.cash_data import CashManager
from core.utility import datatime_to_hms_int
import time
import datetime
import traceback
from threading import Thread
from queue import Queue, Empty
from .base_strategy import BaseStrategy
import logging
logger = logging.getLogger(__name__)

class FundManager:
    """资金管理器"""
    def __init__(self, params):
        self.params = params
        self.total_funds = 10000000  # 总资金1000万，需要从实际账户获取
        self.available_funds = self.total_funds
        self.used_funds = 0
        self.aa_fund_per_part = self.total_funds / params['aa_fund_parts']
        self.aa_used_parts = 0

    def get_aa_fund_amount(self):
        """获取AA模块单次买入金额"""
        if self.aa_used_parts >= self.params['aa_fund_parts']:
            return 0
        amount = min(self.aa_fund_per_part, self.available_funds)

        # 如果每一笔资金等分金额超过200万，则限定单笔最大买入不超过100万
        if amount > self.params['fund_split_threshold']:
            amount = self.params['max_single_amount']
        elif amount > self.params['max_single_amount']:
            amount = self.params['max_single_amount']
        return amount

    def get_bb_fund_amount(self):
        """获取BB模块单次买入金额"""
        amount = self.available_funds * self.params['bb_fund_ratio']
        if amount > self.params['max_single_amount']:
            amount = self.params['max_single_amount']
        return amount

    def use_aa_fund(self, amount):
        """使用AA模块资金"""
        if amount <= self.available_funds:
            self.available_funds -= amount
            self.used_funds += amount
            self.aa_used_parts += 1
            return True
        return False

    def use_bb_fund(self, amount):
        """使用BB模块资金"""
        if amount <= self.available_funds:
            self.available_funds -= amount
            self.used_funds += amount
            return True
        return False

class Strategy001(BaseStrategy):
    def __init__(self) -> None:
        super().__init__()

        # 策略参数配置
        self.strategy_params = {
            # AA模块参数
            'aa_max_turnover_rate': 2.0,  # X% 最大换手率阈值
            'aa_max_amount': 5000,  # Y千万 最大成交额阈值
            'aa_limit_up_turnover_rate': 1.0,  # 涨停价连续N笔委托买一量换手率阈值
            'aa_fund_parts': 4,  # 前4笔交易采用资金N等分，触发一次就用完一次

            # BB模块参数
            'bb_time_window': 30,  # N分钟时间窗口
            'bb_fund_ratio': 0.25,  # 每次使用现金的1/m
            'bb_rise_threshold': 3.0,  # 涨幅阈值X%
            'bb_max_single_turnover': 2.0,  # 单笔最大换手率Y%
            'bb_delay_ticks': 2,  # 延时观察tick数

            # 资金管理参数
            'max_positions': 10,  # 最大持仓个股数N支
            'max_single_amount': 1000000,  # 单笔最大买入100万
            'min_single_amount': 200000,  # 最小单笔金额20万
            'buy_deadline': '143000',  # 买入截止时间
            'fund_split_threshold': 2000000,  # 如果每一笔资金等分金额超过200万，则限定单笔最大买入不超过100万

            # 买入后监控参数
            'post_buy_time_window': 30,  # 买入后N分钟监控窗口
            'post_buy_fall_threshold': 0.03,  # 回落开盘价下方X%
            'post_buy_fall_ratio_threshold': 0.05,  # 最高价和回落价百分比Y%

            # 撤单参数
            'cancel_no_limit_up_time': '150000',  # 未再次封板撤单时间
            'cancel_turnover_threshold': 2.0,  # 撤单换手率阈值X%
            'cancel_consecutive_threshold': 1.0,  # 连续2笔换手率阈值y%
            'cancel_bid_ratio_threshold': 0.5,  # 委托买一量比例阈值
            'cancel_bid_amount_threshold': 100000,  # 委托买一额阈值X万元
        }

        # 策略状态管理
        self.positions = {}  # 持仓记录
        self.orders = {}  # 订单记录
        self.aa_triggered = {}  # AA模块触发记录
        self.bb_candidates = {}  # BB模块候选股票
        self.fund_manager = FundManager(self.strategy_params)

        # 时间管理
        self.market_open_time = self.cover_str_to_ts('093000')
        self.buy_deadline_time = self.cover_str_to_ts(self.strategy_params['buy_deadline'])

        # 板块管理（需要外部配置）
        self.sector_stocks = {}  # 板块股票映射

    def on_tick(self):
        """tick数据处理主循环"""
        while self.active:
            try:
                ticks = self.queue.get(timeout=1)
                if not ticks:
                    continue

                for tick in ticks:
                    self.process_tick(tick)

            except Empty:
                continue
            except Exception as e:
                logger.error(f"处理tick数据异常: {e}")
                traceback.print_exc()

    def process_tick(self, tick: TickData):
        """处理单个tick数据"""
        vt_symbol = tick.vt_symbol

        # 更新cash_tick数据
        if vt_symbol in self.cash_ticks:
            self.cash_ticks[vt_symbol].add_tick(tick)

        # 检查是否超过买入截止时间
        current_time = datatime_to_hms_int(tick.datetime)
        if current_time > int(self.strategy_params['buy_deadline']):
            return

        # AA模块：前4笔逻辑
        self.check_aa_strategy(vt_symbol)

        # BB模块：N分钟模型
        self.check_bb_strategy(vt_symbol)

        # 撤单逻辑
        self.check_cancel_conditions(vt_symbol)

    def check_aa_strategy(self, vt_symbol: str):
        """AA模块：前4笔策略检查"""
        cash_tick = self.cash_ticks.get(vt_symbol)
        if not cash_tick or cash_tick.index < 4:
            return

        # 如果已经触发过AA策略，跳过
        if vt_symbol in self.aa_triggered:
            return

        # 获取前5笔数据（包括092500的第0笔）
        if len(cash_tick.values) < 5:
            return

        t0, t1, t2, t3, t4 = cash_tick.values[:5]

        # 检查涨停价逻辑
        if self.check_limit_up_condition(vt_symbol, [t1, t2, t3, t4]):
            return

        # 主要逻辑判断
        trigger_condition = False

        # 条件1：093000价小于092500价，第2笔价大于092500价
        if t1.last_price < t0.last_price and t2.last_price > t0.last_price:
            trigger_condition = True

        # 条件2：093000和093003都小于092500价，第3笔和第4笔都大于092500价
        elif (t1.last_price < t0.last_price and t2.last_price < t0.last_price and
              t3.last_price > t0.last_price and t4.last_price > t0.last_price):
            trigger_condition = True

        # 价格递增条件检查：1笔>0笔，2笔>1笔，3笔>1笔
        if trigger_condition:
            price_condition_met = (t1.last_price > t0.last_price and
                                 t2.last_price > t1.last_price and
                                 t3.last_price > t1.last_price)

            # 如果基本条件不满足，检查特殊情况
            if not price_condition_met:
                # 如果3笔<2笔价，判定第4笔要大于3笔，否则进入N分钟模型
                if t3.last_price < t2.last_price:
                    if t4.last_price > t3.last_price:
                        # 满足特殊条件，继续AA逻辑
                        pass
                    else:
                        # 不满足，应该进入N分钟模型，这里先跳过AA
                        trigger_condition = False
                else:
                    # 其他不满足基本条件的情况
                    trigger_condition = False

        if trigger_condition:
            # 检查换手率和成交额条件
            max_turnover = max([self.calculate_turnover_rate(vt_symbol, tick) for tick in [t1, t2, t3, t4]])
            max_amount = max([tick.net_amount for tick in [t1, t2, t3, t4]]) / 10000 / 1000  # 转换为千万

            if (max_turnover > self.strategy_params['aa_max_turnover_rate'] and
                max_amount > self.strategy_params['aa_max_amount']):

                self.execute_aa_buy(vt_symbol)

    def check_limit_up_condition(self, vt_symbol: str, ticks: list) -> bool:
        """检查涨停价条件"""
        cash_tick = self.cash_ticks[vt_symbol]
        limit_up_price = cash_tick.limit_up_price

        # 检查是否有tick等于涨停价
        for tick in ticks:
            if abs(tick.last_price - limit_up_price) < 0.01:
                # 计算涨停价连续N笔的委托买一量换手率
                consecutive_turnover = self.calculate_consecutive_bid_turnover(vt_symbol)
                if consecutive_turnover >= self.strategy_params['aa_limit_up_turnover_rate']:
                    self.execute_aa_buy(vt_symbol)
                    return True
        return False

    def check_bb_strategy(self, vt_symbol: str):
        """BB模块：第5笔-N分钟策略检查"""
        cash_tick = self.cash_ticks.get(vt_symbol)
        if not cash_tick or cash_tick.index < 5:
            return

        # 如果已经通过AA模块买入，跳过BB检查
        if vt_symbol in self.aa_triggered:
            return

        current_tick = cash_tick.tick

        # 计算从第5笔开始到当前的时间窗口
        start_time = self.market_open_time + 4 * 3000  # 假设每3秒一笔
        time_window_end = start_time + self.strategy_params['bb_time_window'] * 60 * 1000

        if current_tick.timestamp > time_window_end:
            return

        # 检查涨幅条件
        open_price = cash_tick.values[0].open if cash_tick.values else current_tick.pre_close
        current_rise = (current_tick.last_price - open_price) / open_price * 100
        open_rise = (open_price - current_tick.pre_close) / current_tick.pre_close * 100

        # 检查是否比涨停价小于5档
        limit_up_price = cash_tick.limit_up_price
        price_diff_from_limit = (limit_up_price - current_tick.last_price) / current_tick.last_price * 100

        condition_met = False

        # 条件1：涨幅大于开盘涨幅X%
        if current_rise > open_rise + self.strategy_params['bb_rise_threshold']:
            condition_met = True

        # 条件2：比涨停价小于5档（假设每档0.1%）
        if price_diff_from_limit < 0.5:
            condition_met = True

        if condition_met:
            # 检查期间单笔最大换手率（剔除前4笔）
            max_turnover = self.get_max_turnover_after_tick(vt_symbol, 4)
            if max_turnover > self.strategy_params['bb_max_single_turnover']:
                self.add_bb_candidate(vt_symbol)

    def add_bb_candidate(self, vt_symbol: str):
        """添加BB模块候选股票"""
        if vt_symbol not in self.bb_candidates:
            self.bb_candidates[vt_symbol] = {
                'trigger_time': datetime.datetime.now(),
                'delay_count': 0,
                'sector': self.get_stock_sector(vt_symbol)
            }

        candidate = self.bb_candidates[vt_symbol]
        candidate['delay_count'] += 1

        # 延时观察2个tick后执行
        if candidate['delay_count'] >= self.strategy_params['bb_delay_ticks']:
            self.execute_bb_buy(vt_symbol)

    def execute_aa_buy(self, vt_symbol: str):
        """执行AA模块买入"""
        if len(self.positions) >= self.strategy_params['max_positions']:
            logger.info(f"已达到最大持仓数量，跳过买入 {vt_symbol}")
            return

        amount = self.fund_manager.get_aa_fund_amount()
        if amount == 0:
            logger.info(f"AA模块资金已用完，跳过买入 {vt_symbol}")
            return

        if self.fund_manager.use_aa_fund(amount):
            self.send_buy_order(vt_symbol, amount, "AA")
            self.aa_triggered[vt_symbol] = datetime.datetime.now()
            logger.info(f"AA模块触发买入: {vt_symbol}, 金额: {amount}")

    def execute_bb_buy(self, vt_symbol: str):
        """执行BB模块买入"""
        if len(self.positions) >= self.strategy_params['max_positions']:
            logger.info(f"已达到最大持仓数量，跳过买入 {vt_symbol}")
            return

        # 检查同板块优先级
        if not self.check_sector_priority(vt_symbol):
            logger.info(f"同板块有更优个股，放弃买入 {vt_symbol}")
            return

        amount = self.fund_manager.get_bb_fund_amount()
        if amount == 0:
            logger.info(f"BB模块资金不足，跳过买入 {vt_symbol}")
            return

        if self.fund_manager.use_bb_fund(amount):
            self.send_buy_order(vt_symbol, amount, "BB")
            # 从候选列表中移除
            if vt_symbol in self.bb_candidates:
                del self.bb_candidates[vt_symbol]
            logger.info(f"BB模块触发买入: {vt_symbol}, 金额: {amount}")

    def send_buy_order(self, vt_symbol: str, amount: float, strategy_type: str):
        """发送买入订单"""
        try:
            cash_tick = self.cash_ticks[vt_symbol]
            current_price = cash_tick.tick.last_price
            volume = int(amount / current_price / 100) * 100  # 转换为手数

            if volume < 100:  # 最小1手
                logger.warning(f"买入量不足1手，跳过 {vt_symbol}")
                return

            # 这里需要调用实际的下单接口
            # order_id = self.geteway_api.send_order(...)
            order_id = f"{strategy_type}_{vt_symbol}_{int(time.time())}"

            self.orders[order_id] = {
                'vt_symbol': vt_symbol,
                'volume': volume,
                'price': current_price,
                'amount': amount,
                'strategy_type': strategy_type,
                'order_time': datetime.datetime.now(),
                'status': 'submitted'
            }

            self.positions[vt_symbol] = {
                'volume': volume,
                'avg_price': current_price,
                'strategy_type': strategy_type,
                'buy_time': datetime.datetime.now()
            }

        except Exception as e:
            logger.error(f"发送买入订单失败 {vt_symbol}: {e}")

    def check_cancel_conditions(self, vt_symbol: str):
        """检查撤单条件"""
        if vt_symbol not in self.orders:
            return

        current_time = datatime_to_hms_int(datetime.datetime.now())

        # 条件AA：指定时刻没有再次封板就撤单
        cancel_time = int(self.strategy_params['cancel_no_limit_up_time'])
        if current_time >= cancel_time:
            if not self.is_limit_up(vt_symbol):
                self.cancel_order(vt_symbol, "未再次封板")
                return

        # 条件BB：各种撤单条件检查
        cash_tick = self.cash_ticks[vt_symbol]
        current_tick = cash_tick.tick

        # 单笔成交量换手率大于X%
        current_turnover = self.calculate_turnover_rate(vt_symbol, current_tick)
        if current_turnover > self.strategy_params['cancel_turnover_threshold']:
            self.cancel_order(vt_symbol, f"单笔换手率过大: {current_turnover}%")
            return

        # 连续2笔换手率>y%
        if self.check_consecutive_high_turnover(vt_symbol):
            self.cancel_order(vt_symbol, "连续高换手率")
            return

        # 委托买一量和买一额条件
        if self.check_bid_volume_condition(vt_symbol):
            self.cancel_order(vt_symbol, "委托买一量条件触发")
            return

    def calculate_turnover_rate(self, vt_symbol: str, tick: TickData) -> float:
        """计算换手率"""
        cash_tick = self.cash_ticks[vt_symbol]
        if cash_tick.float_volume == 0:
            return 0
        return tick.net_volume / cash_tick.float_volume * 100

    def calculate_consecutive_bid_turnover(self, vt_symbol: str) -> float:
        """计算涨停价连续N笔的委托买一量换手率"""
        cash_tick = self.cash_ticks[vt_symbol]
        limit_up_price = cash_tick.limit_up_price

        consecutive_volume = 0
        consecutive_count = 0

        # 从最新的tick开始往前查找连续涨停价的tick
        for i in range(len(cash_tick.values) - 1, -1, -1):
            tick = cash_tick.values[i]
            if abs(tick.bid_price_1 - limit_up_price) < 0.01:
                consecutive_volume += tick.bid_volume_1
                consecutive_count += 1
            else:
                break

        if cash_tick.float_volume == 0:
            return 0
        return consecutive_volume / cash_tick.float_volume * 100

    def get_max_turnover_after_tick(self, vt_symbol: str, start_tick_index: int) -> float:
        """获取指定tick之后的最大换手率"""
        cash_tick = self.cash_ticks[vt_symbol]
        max_turnover = 0

        for i in range(start_tick_index, len(cash_tick.values)):
            tick = cash_tick.values[i]
            turnover = self.calculate_turnover_rate(vt_symbol, tick)
            max_turnover = max(max_turnover, turnover)

        return max_turnover

    def get_stock_sector(self, vt_symbol: str) -> str:
        """获取股票所属板块"""
        # 这里需要根据实际情况实现板块映射
        for sector, stocks in self.sector_stocks.items():
            if vt_symbol in stocks:
                return sector
        return "unknown"

    def check_sector_priority(self, vt_symbol: str) -> bool:
        """检查同板块优先级"""
        sector = self.get_stock_sector(vt_symbol)
        if sector == "unknown":
            return True

        # 检查同板块是否有更优的候选股票
        for symbol, candidate in self.bb_candidates.items():
            if (candidate['sector'] == sector and symbol != vt_symbol and
                candidate['delay_count'] >= self.strategy_params['bb_delay_ticks']):
                # 比较优先级（这里可以根据具体需求实现）
                if self.compare_stock_priority(symbol, vt_symbol):
                    return False
        return True

    def compare_stock_priority(self, stock1: str, stock2: str) -> bool:
        """比较股票优先级，返回True表示stock1优先级更高"""
        # 这里可以根据具体需求实现优先级比较逻辑
        # 例如：涨幅、换手率、成交额等指标
        cash_tick1 = self.cash_ticks.get(stock1)
        cash_tick2 = self.cash_ticks.get(stock2)

        if not cash_tick1 or not cash_tick2:
            return False

        # 简单示例：比较当前涨幅
        rise1 = (cash_tick1.tick.last_price - cash_tick1.tick.pre_close) / cash_tick1.tick.pre_close * 100
        rise2 = (cash_tick2.tick.last_price - cash_tick2.tick.pre_close) / cash_tick2.tick.pre_close * 100

        return rise1 > rise2

    def is_limit_up(self, vt_symbol: str) -> bool:
        """判断是否涨停"""
        cash_tick = self.cash_ticks[vt_symbol]
        current_tick = cash_tick.tick
        return abs(current_tick.last_price - cash_tick.limit_up_price) < 0.01

    def check_consecutive_high_turnover(self, vt_symbol: str) -> bool:
        """检查连续2笔换手率是否大于阈值"""
        cash_tick = self.cash_ticks[vt_symbol]
        if len(cash_tick.values) < 2:
            return False

        last_two_ticks = cash_tick.values[-2:]
        for tick in last_two_ticks:
            turnover = self.calculate_turnover_rate(vt_symbol, tick)
            if turnover <= self.strategy_params['cancel_consecutive_threshold']:
                return False
        return True

    def check_bid_volume_condition(self, vt_symbol: str) -> bool:
        """检查委托买一量条件"""
        cash_tick = self.cash_ticks[vt_symbol]
        current_tick = cash_tick.tick

        # 委托买一量比最大买一量小于X%
        max_bid_volume = current_tick.max_bid_volume_1
        if max_bid_volume > 0:
            bid_ratio = current_tick.bid_volume_1 / max_bid_volume
            if bid_ratio < self.strategy_params['cancel_bid_ratio_threshold']:
                # 同时委托买一额小于X万元
                bid_amount = current_tick.bid_volume_1 * current_tick.bid_price_1
                if bid_amount < self.strategy_params['cancel_bid_amount_threshold']:
                    return True
        return False

    def cancel_order(self, vt_symbol: str, reason: str):
        """撤销订单"""
        try:
            # 这里需要调用实际的撤单接口
            # self.geteway_api.cancel_order(...)

            logger.info(f"撤销订单 {vt_symbol}: {reason}")

            # 更新订单状态
            for order in self.orders.values():
                if order['vt_symbol'] == vt_symbol and order['status'] == 'submitted':
                    order['status'] = 'cancelled'
                    order['cancel_reason'] = reason
                    order['cancel_time'] = datetime.datetime.now()

            # 从持仓中移除
            if vt_symbol in self.positions:
                del self.positions[vt_symbol]

        except Exception as e:
            logger.error(f"撤销订单失败 {vt_symbol}: {e}")

    def start(self):
        """启动策略"""
        self.active = True
        logger.info("Strategy001 启动")

        # 订阅行情数据
        if self.geteway_api:
            self.geteway_api.add_queue(self.name, self.queue)

        # 启动线程
        for thread in self.thread_lst:
            if not thread.is_alive():
                thread.start()

    def stop(self):
        """停止策略"""
        self.active = False
        logger.info("Strategy001 停止")

        # 取消订阅
        if self.geteway_api:
            self.geteway_api.remove_queue(self.name)

        # 等待线程结束
        for thread in self.thread_lst:
            if thread.is_alive():
                thread.join(timeout=5)

    def check_post_buy_conditions(self, vt_symbol: str):
        """检查买入后的条件（第5条规则）
        第一笔买入成交后，如果后面N分钟之内股价回落开盘价下方X%，
        并且最高价和当时的回落价百分比小于Y%，该价格大于昨日最低价，
        同时所有阳量大于阴量继续成交一笔。此前最高价必须大于开盘价。
        """
        if vt_symbol not in self.positions:
            return

        position = self.positions[vt_symbol]
        buy_time = position['buy_time']
        time_window = self.strategy_params['post_buy_time_window'] * 60  # 转换为秒

        # 检查是否在N分钟内
        if (datetime.datetime.now() - buy_time).seconds > time_window:
            return

        cash_tick = self.cash_ticks[vt_symbol]
        current_tick = cash_tick.tick

        # 获取开盘价和最高价
        open_price = cash_tick.values[0].open if cash_tick.values else current_tick.pre_close

        # 计算买入后的最高价（从买入时间开始）
        high_price_after_buy = current_tick.high
        for tick in cash_tick.values:
            if tick.datetime >= buy_time:
                high_price_after_buy = max(high_price_after_buy, tick.high)

        # 检查股价是否回落到开盘价下方X%
        fall_threshold = self.strategy_params['post_buy_fall_threshold']
        if current_tick.last_price < open_price * (1 - fall_threshold):
            # 检查最高价和当前回落价的百分比
            fall_ratio = (high_price_after_buy - current_tick.last_price) / high_price_after_buy
            if fall_ratio < self.strategy_params['post_buy_fall_ratio_threshold']:
                # 检查价格是否大于昨日最低价（这里需要实际的昨日数据）
                # 暂时使用跌停价作为昨日最低价的近似
                yesterday_low = cash_tick.limit_down_price if cash_tick.limit_down_price > 0 else current_tick.pre_close * 0.9
                if current_tick.last_price > yesterday_low:
                    # 检查阳量是否大于阴量
                    if self.check_yang_volume_greater(vt_symbol):
                        # 此前最高价必须大于开盘价
                        if high_price_after_buy > open_price:
                            self.execute_additional_buy(vt_symbol)

    def check_yang_volume_greater(self, vt_symbol: str) -> bool:
        """检查所有阳量是否大于阴量"""
        cash_tick = self.cash_ticks[vt_symbol]
        total_yang_volume = sum([tick.yang_volume for tick in cash_tick.values])
        total_yin_volume = sum([tick.yin_volume for tick in cash_tick.values])
        return total_yang_volume > total_yin_volume

    def execute_additional_buy(self, vt_symbol: str):
        """执行额外买入"""
        amount = self.fund_manager.get_bb_fund_amount()
        if amount > 0 and self.fund_manager.use_bb_fund(amount):
            self.send_buy_order(vt_symbol, amount, "ADDITIONAL")
            logger.info(f"执行额外买入: {vt_symbol}, 金额: {amount}")

    def get_strategy_status(self) -> dict:
        """获取策略状态"""
        return {
            'active': self.active,
            'positions_count': len(self.positions),
            'orders_count': len(self.orders),
            'aa_triggered_count': len(self.aa_triggered),
            'bb_candidates_count': len(self.bb_candidates),
            'available_funds': self.fund_manager.available_funds,
            'used_funds': self.fund_manager.used_funds,
            'aa_used_parts': self.fund_manager.aa_used_parts,
        }

    def update_sector_stocks(self, sector_mapping: dict):
        """更新板块股票映射"""
        self.sector_stocks = sector_mapping
        logger.info(f"更新板块映射，包含 {len(sector_mapping)} 个板块")

    def run(self):
        """运行策略（保持原有接口兼容性）"""
        self.start()

        # 启动线程
        for thread in self.thread_lst:
            if not thread.is_alive():
                thread.start()

        logger.info("Strategy001 运行中...")

        try:
            # 主循环
            while self.active:
                time.sleep(1)

                # 定期检查买入后条件
                for vt_symbol in list(self.positions.keys()):
                    self.check_post_buy_conditions(vt_symbol)

        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.stop()
